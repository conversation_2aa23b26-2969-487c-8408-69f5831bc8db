import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  BookOpen,
  Lock,
  ChevronUp,
  ChevronDown,
  Maximize,
  Minimize
} from 'lucide-react';
import { ModuleSectionProps } from '@/types/lms';
import { ChapterSection } from './chapter-section';
import { QuizCard } from './quiz-card';

export const ModuleSection: React.FC<ModuleSectionProps> = ({
  module,
  expandedContents,
  expandedChapters,
  onToggleContent,
  onToggleContentComplete,
  onStartQuiz,
  isExpanded,
  onToggleExpanded,
  onToggleChapter,
  onExpandAllChapters,
  onCollapseAllChapters
}) => {
  const totalChapters = module.chapters.length;
  const completedChapters = module.chapters.filter(
    (ch) => ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)
  ).length;
  const progress =
    totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;

  // Only check unlocked chapters for expansion state
  const unlockedChapters = module.chapters.filter(ch => ch.isUnlocked);
  const allChaptersExpanded = unlockedChapters.length > 0 && unlockedChapters.every(
    (ch) => expandedChapters[ch.id] === true
  );


  return (
    <Card
      id={`module-${module.id}`}
      className={`mb-6 shadow-md scroll-mt-20 ${module.isUnlocked ? 'border-green-200' : 'border-gray-200'}`}
    >
      <CardHeader
        className={`border-b ${!module.isUnlocked ? 'opacity-60' : ''}`}
      >
        <div
          className='flex cursor-pointer items-center justify-between'
          onClick={() => module.isUnlocked && onToggleExpanded()}
        >
          <div className='flex flex-1 items-center space-x-4'>
            <div
              className={`rounded-lg p-3 ${module.isUnlocked ? 'bg-purple-100 text-purple-700' : 'bg-gray-100 text-gray-500'}`}
            >
              <BookOpen className='h-6 w-6' />
            </div>
            <div className='flex-1'>
              <div className='flex items-center space-x-2'>
                <CardTitle className='text-lg'>{module.title}</CardTitle>
                {!module.isUnlocked && (
                  <Lock className='h-4 w-4 text-gray-400' />
                )}
              </div>
              <p className='mt-1 text-gray-600'>{module.description}</p>
              <div className='mt-3 max-w-md'>
                <Progress value={progress} className='h-3' />
              </div>
              <p className='mt-2 text-sm text-gray-500'>
                {completedChapters}/{totalChapters} chapters completed
                {module.moduleQuiz.isPassed && ' • Module quiz passed'}
              </p>
            </div>
          </div>
          {module.isUnlocked &&
            (isExpanded ? (
              <ChevronUp className='h-6 w-6 text-gray-400' />
            ) : (
              <ChevronDown className='h-6 w-6 text-gray-400' />
            ))}
        </div>
      </CardHeader>

      {isExpanded && module.isUnlocked && (
        <CardContent className='pt-6'>
          {/* Chapter Expand/Collapse Controls */}
          <div className='mb-4 flex justify-end space-x-2'>
            <Button
              size='sm'
              variant='outline'
              onClick={() => {
                if (allChaptersExpanded) {
                  onCollapseAllChapters();
                } else {
                  onExpandAllChapters();
                }
              }}
              className='border-purple-200 text-purple-600 hover:bg-purple-50'
            >
              {allChaptersExpanded ? (
                <>
                  <Minimize className='mr-2 h-4 w-4' />
                  Tutup Semua Bab
                </>
              ) : (
                <>
                  <Maximize className='mr-2 h-4 w-4' />
                  Buka Semua Bab
                </>
              )}
            </Button>
          </div>

          {/* Module Chapters */}
          <div className='space-y-2'>
            {module.chapters.map((chapter) => (
              <ChapterSection
                key={chapter.id}
                chapter={chapter}
                expandedContents={expandedContents}
                onToggleContent={onToggleContent}
                onToggleContentComplete={onToggleContentComplete}
                onStartQuiz={onStartQuiz}
                isExpanded={expandedChapters[chapter.id] || false}
                onToggleExpanded={() => onToggleChapter(chapter.id)}
              />
            ))}
          </div>

          {/* Module Quiz */}
          <div className='mt-6 border-t pt-4'>
            <h4 className='mb-3 font-medium text-purple-700'>
              Module Assessment
            </h4>
            <QuizCard
              quiz={module.moduleQuiz}
              isUnlocked={completedChapters === totalChapters}
              onStartQuiz={() => onStartQuiz(module.moduleQuiz.id)}
            />
          </div>
        </CardContent>
      )}
    </Card>
  );
};
